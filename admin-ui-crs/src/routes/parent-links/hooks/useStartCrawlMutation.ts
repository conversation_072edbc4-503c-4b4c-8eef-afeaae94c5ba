import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import type { CrawlRequest } from "../api/parent-links";
import { startCrawl } from "../api/parent-links";

export function useStartCrawlMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CrawlRequest) => startCrawl(data),
    onSuccess: (data: { task_id: string }) => {
      toast.success("Crawl started successfully", {
        description: `Task ID: ${data.task_id}`,
      });
      queryClient.invalidateQueries({ queryKey: ['parent-links'] });
    },
    onError: (error: Error) => {
      toast.error("Failed to start crawl", {
        description: error.message || "An unknown error occurred",
      });
      console.error("Error starting crawl:", error);
    },
  });
}
