import { apiRequest } from '@/config/httpClient';

export interface CrawlRequest {
  url: string;
  follow_links: boolean;
  max_follow_links?: number;
}

export interface CrawlResponse {
  task_id: string;
}

export interface ParentLink {
  id: string;
  original_url: string;
  final_url: string;
  title: string;
  content: string;
  crawled_at?: string;
  created_at: string;
  updated_at: string;
}

export const fetchParentLinks = async (): Promise<{ parent_links: ParentLink[] }> => {
  return apiRequest<{ parent_links: ParentLink[] }>({
    method: 'get',
    url: '/parent-links',
  });
};

export const startCrawl = async (data: CrawlRequest): Promise<CrawlResponse> => {
  return apiRequest<CrawlResponse>({
    method: 'post',
    url: '/crawl',
    data: {
      url: data.url,
      follow_links: data.follow_links,
      max_follow_links: data.follow_links ? data.max_follow_links : undefined,
    },
  });
};
