import { format } from "date-fns";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";

export interface ParentLink {
  id: string;
  original_url: string;
  final_url: string;
  title: string;
  content: string;
  crawled_at?: string;
  created_at: string;
  updated_at: string;
}

interface ParentLinksTableProps {
  parentLinks: ParentLink[];
  loading: boolean;
}

export function ParentLinksTable({ parentLinks, loading }: ParentLinksTableProps) {
  if (loading) {
    return (
      <div className="space-y-2">
        {[...Array(5)].map((_, i) => (
          <Skeleton key={i} className="h-16 w-full" />
        ))}
      </div>
    );
  }

  if (parentLinks.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No parent links found.
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Title</TableHead>
            <TableHead>URL</TableHead>
            <TableHead>Last Crawled</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {parentLinks.map((link) => (
            <TableRow key={link.id}>
              <TableCell className="font-medium max-w-xs truncate">
                {link.title || 'N/A'}
              </TableCell>
              <TableCell className="max-w-xs">
                <a 
                  href={link.original_url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline truncate block max-w-xs"
                  title={link.original_url}
                >
                  {link.original_url}
                </a>
              </TableCell>
              <TableCell>
                {link.crawled_at ? format(new Date(link.crawled_at), 'PPpp') : 'Never'}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
