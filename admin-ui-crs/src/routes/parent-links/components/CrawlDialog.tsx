import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useStartCrawlMutation } from "../hooks/useStartCrawlMutation";

interface CrawlDialogProps {
  onSuccess?: () => void;
  children?: React.ReactNode;
}

export function CrawlDialog({ onSuccess, children }: CrawlDialogProps) {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState({
    url: "",
    follow_links: false,
    max_follow_links: 10,
  });

  const { mutate: startCrawl, isPending } = useStartCrawlMutation();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    startCrawl({
      url: formData.url,
      follow_links: formData.follow_links,
      max_follow_links: formData.max_follow_links,
    }, {
      onSuccess: () => {
        setOpen(false);
        onSuccess?.();
      },
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children || <Button>New Crawl</Button>}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Start New Crawl</DialogTitle>
            <DialogDescription>
              Enter the URL you want to crawl and configure the crawl settings.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="url">URL</Label>
              <Input
                id="url"
                type="url"
                placeholder="https://example.com"
                value={formData.url}
                onChange={(e) =>
                  setFormData({ ...formData, url: e.target.value })
                }
                required
              />
            </div>
            <div className="flex items-center space-x-2">
              <input
                id="follow-links"
                type="checkbox"
                className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                checked={formData.follow_links}
                onChange={(e) =>
                  setFormData({ ...formData, follow_links: e.target.checked })
                }
              />
              <Label htmlFor="follow-links" className="text-sm font-medium text-gray-700">
                Follow links
              </Label>
            </div>
            {formData.follow_links && (
              <div className="space-y-2">
                <Label htmlFor="max-follow-links">
                  Max links to follow (max 50)
                </Label>
                <Input
                  id="max-follow-links"
                  type="number"
                  min="1"
                  max="50"
                  value={formData.max_follow_links}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      max_follow_links: Math.min(
                        Math.max(1, Number(e.target.value)),
                        50
                      ),
                    })
                  }
                />
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isPending}>
              {isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Starting...
                </>
              ) : (
                'Start Crawl'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
