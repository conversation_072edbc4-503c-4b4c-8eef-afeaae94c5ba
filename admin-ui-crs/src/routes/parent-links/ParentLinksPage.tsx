import { ErrorDisplay } from "@/components/error-display";
import { ParentLinksTable } from "./components/ParentLinksTable";
import { Button } from "@/components/ui/button";
import { Plus, RefreshCw } from "lucide-react";
import { useParentLinksQuery } from "./hooks/useParentLinksQuery";
import { CrawlDialog } from "./components/CrawlDialog";

export default function ParentLinksPage() {
  const { 
    data, 
    isLoading, 
    isError, 
    error, 
    isRefetching, 
    refetch 
  } = useParentLinksQuery();

  if (isError) {
    return <ErrorDisplay error={error} />;
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Parent Links</h1>
        <div className="flex items-center gap-2">
          <CrawlDialog onSuccess={refetch}>
            <Button size="sm" className="gap-2">
              <Plus className="h-4 w-4" />
              New Crawl
            </Button>
          </CrawlDialog>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => refetch()}
            disabled={isRefetching}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isRefetching ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
        <></>
      </div>
      <ParentLinksTable 
        parentLinks={data?.parent_links || []} 
        loading={isLoading || isRefetching} 
      />
    </div>
  );
}
