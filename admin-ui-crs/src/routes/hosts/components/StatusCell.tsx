import type { Row } from '@tanstack/react-table';
import type { Host } from '../api/hosts';

interface StatusCellProps {
  row: Row<Host>;
}

export function StatusCell({ row }: StatusCellProps) {
  return (
    <div className="flex items-center">
      <div 
        className={`h-2.5 w-2.5 rounded-full mr-2 ${
          row.original.is_active ? 'bg-green-500' : 'bg-gray-400'
        }`} 
      />
      {row.original.is_active ? 'Active' : 'Inactive'}
    </div>
  );
}
