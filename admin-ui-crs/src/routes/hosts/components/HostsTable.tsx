import { useState } from "react";
import { DataTable } from "@/components/ui/data-table";
import { Button } from "@/components/ui/button";
import { Pencil, Trash2 } from "lucide-react";
import type { ColumnDef } from "@tanstack/react-table";
import type { Host } from "../api/hosts";
import { StatusCell } from "./StatusCell";
import { EditHostDialog } from "./EditHostDialog";
import { DeleteHostDialog } from "./DeleteHostDialog";

const columns: ColumnDef<Host>[] = [
  {
    accessorKey: "id",
    header: "ID",
  },
  {
    accessorKey: "hostname",
    header: "Hostname",
  },
  {
    accessorKey: "ip_address",
    header: "IP Address",
  },
  {
    accessorKey: "is_active",
    header: "Status",
    cell: StatusCell,
  },
  {
    accessorKey: "last_seen",
    header: "Last Seen",
    cell: ({ row }) => row.original.last_seen || 'Never',
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const host = row.original;
      const [isEditOpen, setIsEditOpen] = useState(false);
      const [isDeleteOpen, setIsDeleteOpen] = useState(false);

      return (
        <div className="flex space-x-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsEditOpen(true)}
            title="Edit host"
          >
            <Pencil className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsDeleteOpen(true)}
            title="Delete host"
            className="text-red-500 hover:text-red-600 hover:bg-red-100"
          >
            <Trash2 className="h-4 w-4" />
          </Button>

          <EditHostDialog
            host={host}
            open={isEditOpen}
            onOpenChange={setIsEditOpen}
          />
          <DeleteHostDialog
            hostId={host.id.toString()}
            hostname={host.hostname || host.domain || 'this host'}
            open={isDeleteOpen}
            onOpenChange={setIsDeleteOpen}
          />
        </div>
      );
    },
  },
];

interface HostsTableProps {
  hosts: Host[];
  loading?: boolean;
}

export function HostsTable({ hosts, loading }: HostsTableProps) {
  return <DataTable columns={columns} data={hosts} loading={loading} />;
}
