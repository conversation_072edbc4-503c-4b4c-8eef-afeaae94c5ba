import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";
import { useUpdateHost } from "../hooks/useUpdateHost";
import type { Host, UpdateHostInput } from "../api/hosts";

interface EditHostDialogProps {
  host: Host;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function EditHostDialog({ host, open, onOpenChange }: EditHostDialogProps) {
  const [formData, setFormData] = useState<UpdateHostInput>({
    hostname: host.hostname,
    domain: host.domain,
    protocol: host.protocol || 'https',
    port: host.port,
    description: host.description,
    is_active: host.is_active,
  });

  const { mutate: updateHost, isPending } = useUpdateHost();

  useEffect(() => {
    if (open) {
      setFormData({
        hostname: host.hostname,
        domain: host.domain,
        protocol: host.protocol || 'https',
        port: host.port,
        description: host.description,
        is_active: host.is_active,
      });
    }
  }, [open, host]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateHost({
      id: host.id.toString(),
      data: formData,
    }, {
      onSuccess: () => onOpenChange(false),
    });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    
    setFormData(prev => {
      let newValue: string | number | boolean | null = value;
      
      if (type === 'checkbox') {
        newValue = checked;
      } else if (name === 'port') {
        newValue = value ? parseInt(value) || null : null;
      } else if (value === '') {
        newValue = null;
      }
      
      return {
        ...prev,
        [name]: newValue,
      };
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Edit Host</DialogTitle>
            <DialogDescription>
              Update the host information below.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="hostname" className="text-right">
                Hostname
              </Label>
              <Input
                id="hostname"
                name="hostname"
                value={formData.hostname || ''}
                onChange={handleChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="domain" className="text-right">
                Domain
              </Label>
              <Input
                id="domain"
                name="domain"
                value={formData.domain || ''}
                onChange={handleChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="protocol" className="text-right">
                Protocol
              </Label>
              <Input
                id="protocol"
                name="protocol"
                value={formData.protocol || ''}
                onChange={handleChange}
                className="col-span-3"
                placeholder="https"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="port" className="text-right">
                Port
              </Label>
              <Input
                id="port"
                name="port"
                type="number"
                value={formData.port?.toString() || ''}
                onChange={handleChange}
                className="col-span-3"
                placeholder="443"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              <Input
                id="description"
                name="description"
                value={formData.description || ''}
                onChange={handleChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="is_active" className="text-right">
                Active
              </Label>
              <div className="col-span-3 flex items-center space-x-2">
                <input
                  id="is_active"
                  name="is_active"
                  type="checkbox"
                  checked={formData.is_active}
                  onChange={handleChange}
                  className="h-4 w-4"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" disabled={isPending}>
              {isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                'Update Host'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
