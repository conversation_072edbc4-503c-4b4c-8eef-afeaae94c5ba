export interface Host {
  id: number;
  hostname: string | null;
  domain: string | null;
  protocol: string | null;
  port: number | null;
  description: string | null;
  ip_address: string;
  is_active: boolean;
  last_seen: string | null;
  created_at: string;
  updated_at: string;
}

export type UpdateHostInput = {
  hostname: string | null;
  domain: string | null;
  protocol: string | null;
  port: number | null;
  description: string | null;
  is_active: boolean;
};

interface ApiResponse {
  hosts: Host[];
  total: number;
  skip: number;
  limit: number;
}

import { apiRequest } from '@/config/httpClient';

export const fetchHosts = async (): Promise<ApiResponse> => {
  return apiRequest<ApiResponse>({
    method: 'get',
    url: '/hosts',
    params: {
      skip: 0,
      limit: 100,
      active_only: false
    }
  });
};

export const addHostFromUrl = async (url: string): Promise<Host> => {
  return apiRequest<Host>({
    method: 'post',
    url: '/hosts/from-url',
    params: { url },
    data: {}
  });
};
