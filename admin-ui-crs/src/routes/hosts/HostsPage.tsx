import {
  HostsTable,
  ErrorDisplay,
  AddHostDialog,
} from "./components";
import { useHostsQuery } from "./hooks/useHostsQuery";

export default function HostsPage() {
  const { data, isLoading, isError, error, isRefetching } = useHostsQuery();

  if (isError) {
    return <ErrorDisplay error={error} />;
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Hosts</h1>
        <AddHostDialog />
      </div>
      <div></div>
      <HostsTable hosts={data?.hosts || []} loading={isLoading || isRefetching} />
    </div>
  );
}
