import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { addHostFromUrl } from "../api/hosts";

export function useAddHostMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: addHostFromUrl,
    onSuccess: () => {
      toast.success("Host added successfully!");
      queryClient.invalidateQueries({ queryKey: ['hosts'] });
      return { success: true };
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to add host");
      return { success: false, error };
    },
  });
}
