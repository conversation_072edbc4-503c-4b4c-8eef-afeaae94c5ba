import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import type { UpdateHostInput } from "../api/hosts";
import httpClient from "@/config/httpClient";

export function useUpdateHost() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateHostInput }) => {
      const response = await httpClient.put(`/hosts/${id}`, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["hosts"] });
      toast.success("Host updated successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to update host: ${error.message}`);
    },
  });
}
