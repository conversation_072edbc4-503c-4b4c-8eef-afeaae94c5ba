import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import httpClient from "@/config/httpClient";

export function useDeleteHost() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      await httpClient.delete(`/hosts/${id}`);
      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["hosts"] });
      toast.success("Host deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete host: ${error.message}`);
    },
  });
}
