import { useQuery } from "@tanstack/react-query";
import { useSearchParams } from "react-router-dom";
import { 
  fetchArchiveLinks, 
  type ArchiveLink, 
  type ArchiveLinksResponse, 
  type ArchiveLinksParams 
} from "../api/archive-links";

export { type ArchiveLink };

export function useArchiveLinksQuery() {
  const [searchParams] = useSearchParams();
  
  const page = parseInt(searchParams.get('page') || '1');
  const pageSize = parseInt(searchParams.get('pageSize') || '10');
  
  const sortOrder = searchParams.get('sort_order');
  
  const params: ArchiveLinksParams = {
    skip: (page - 1) * pageSize,
    limit: pageSize,
    parent_id: searchParams.get('parent_id') || undefined,
    domain: searchParams.get('domain') || undefined,
    link_type: searchParams.get('link_type') || undefined,
    is_image: searchParams.get('is_image') ? '1' : undefined,
    status_code: searchParams.get('status_code') || undefined,
    search: searchParams.get('search') || undefined,
    sort_by: searchParams.get('sort_by') || 'crawled_at',
    sort_order: (sortOrder === 'asc' || sortOrder === 'desc') ? sortOrder : 'desc',
  };

  return useQuery<ArchiveLinksResponse>({
    queryKey: ['archive-links', Object.fromEntries(searchParams)],
    queryFn: () => fetchArchiveLinks(params),
    placeholderData: (previousData) => previousData,
  });
}
