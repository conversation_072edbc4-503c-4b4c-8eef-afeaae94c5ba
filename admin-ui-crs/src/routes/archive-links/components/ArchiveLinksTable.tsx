import { useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import type { ArchiveLink } from "../api/archive-links";

interface ArchiveLinksTableProps {
  archiveLinks: ArchiveLink[];
  loading: boolean;
  total: number;
}

export function ArchiveLinksTable({ archiveLinks, loading, total }: ArchiveLinksTableProps) {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchInput, setSearchInput] = useState(searchParams.get('search') || '');
  
  const page = parseInt(searchParams.get('page') || '1');
  const pageSize = parseInt(searchParams.get('pageSize') || '10');
  const totalPages = Math.ceil(total / pageSize);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const params = new URLSearchParams(searchParams);
    if (searchInput) {
      params.set('search', searchInput);
    } else {
      params.delete('search');
    }
    params.delete('page'); // Reset to first page on new search
    setSearchParams(params);
  };

  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(searchParams);
    params.set('page', newPage.toString());
    setSearchParams(params);
  };

  const handleSort = (field: string) => {
    const params = new URLSearchParams(searchParams);
    if (params.get('sort_by') === field) {
      params.set('sort_order', params.get('sort_order') === 'asc' ? 'desc' : 'asc');
    } else {
      params.set('sort_by', field);
      params.set('sort_order', 'asc');
    }
    setSearchParams(params);
  };

  const renderSortIcon = (field: string) => {
    if (searchParams.get('sort_by') !== field) return null;
    return searchParams.get('sort_order') === 'asc' ? '↑' : '↓';
  };

  if (loading && archiveLinks.length === 0) {
    return (
      <div className="space-y-2">
        {[...Array(5)].map((_, i) => (
          <Skeleton key={i} className="h-12 w-full" />
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <form onSubmit={handleSearch} className="flex gap-2 mb-4">
        <Input
          placeholder="Search links..."
          value={searchInput}
          onChange={(e) => setSearchInput(e.target.value)}
          className="max-w-sm"
        />
        <Button type="submit" variant="outline">
          Search
        </Button>
      </form>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead 
                className="cursor-pointer hover:bg-accent"
                onClick={() => handleSort('url')}
              >
                URL {renderSortIcon('url')}
              </TableHead>
              <TableHead>Domain</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Status</TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-accent"
                onClick={() => handleSort('crawled_at')}
              >
                Crawled {renderSortIcon('crawled_at')}
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {archiveLinks.map((link) => (
              <TableRow 
                key={link.id} 
                onClick={() => navigate(`/archive-links/${link.id}`)}
                className="cursor-pointer hover:bg-accent transition-colors"
              >
                <TableCell className="font-medium">
                  <div className="line-clamp-1">
                    {link.url}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="line-clamp-1">
                    {link.domain}
                  </div>
                </TableCell>
                <TableCell>{link.link_type}</TableCell>
                <TableCell>{link.status_code || '-'}</TableCell>
                <TableCell>
                  {link.crawled_at ? new Date(link.crawled_at).toLocaleString() : 'Never'}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between px-2">
        <div className="text-sm text-muted-foreground">
          Showing <span className="font-medium">{(page - 1) * pageSize + 1}</span> to{" "}
          <span className="font-medium">
            {Math.min(page * pageSize, total)}
          </span>{" "}
          of <span className="font-medium">{total}</span> links
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(page - 1)}
            disabled={page <= 1}
          >
            Previous
          </Button>
          <span className="text-sm">
            Page {page} of {totalPages || 1}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(page + 1)}
            disabled={page >= totalPages}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
