import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ChevronLeft } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { getArchiveLinkById } from '../api/archive-links';


export function ArchiveLinkDetail() {
  const { id } = useParams<{ id: string }>();
  
  const {
    data: response,
    isLoading,
    isError,
    error: queryError
  } = useQuery({
    queryKey: ['archiveLink', id],
    queryFn: () => getArchiveLinkById(parseInt(id!)),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1
  });

  const archiveLink = response;

  if (isLoading) {
    return (
      <div className="container mx-auto p-6 space-y-4">
        <Skeleton className="h-10 w-48" />
        <div className="space-y-2">
          <Skeleton className="h-6 w-full" />
          <Skeleton className="h-6 w-3/4" />
          <Skeleton className="h-6 w-1/2" />
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="container mx-auto p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">
            {queryError instanceof Error ? queryError.message : 'Failed to load archive link. Please try again.'}
          </span>
        </div>
      </div>
    );
  }

  if (!archiveLink) {
    return (
      <div className="container mx-auto p-6">
        <p>Archive link not found</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center space-x-4">
        <Button asChild variant="ghost" size="icon" className="h-8 w-8">
          <Link to="/archive-links">
            <ChevronLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-2xl font-bold">Archive Link Details</h1>
      </div>

      <div className="border rounded-lg overflow-hidden mb-6">
        <div className="px-6 py-4 border-b">
          <h2 className="text-lg font-semibold">Basic Information</h2>
        </div>
        <div className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">URL</p>
              <p className="break-all">
                <a 
                  href={archiveLink.url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                >
                  {archiveLink.url}
                </a>
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Title</p>
              <p>{archiveLink.title || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Domain</p>
              <p>{archiveLink.domain}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Link Type</p>
              <p>{archiveLink.link_type}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Status Code</p>
              <p>{archiveLink.status_code || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Content Type</p>
              <p>{archiveLink.content_type || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">File Size</p>
              <p>{archiveLink.file_size ? `${archiveLink.file_size} bytes` : 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Is Image</p>
              <p>{archiveLink.is_image ? 'Yes' : 'No'}</p>
            </div>
          </div>
        </div>
      </div>

      {archiveLink.content && (
        <div className="border rounded-lg overflow-hidden mb-6">
          <div className="px-6 py-4 border-b">
            <h2 className="text-lg font-semibold">Content</h2>
          </div>
          <div className="p-6">
            <div className="whitespace-pre-wrap bg-muted/50 p-4 rounded-md">
              {archiveLink.content}
            </div>
          </div>
        </div>
      )}

      {archiveLink.markdown_content && (
        <div className="border rounded-lg overflow-hidden mb-6">
          <div className="px-6 py-4 border-b">
            <h2 className="text-lg font-semibold">Markdown Content</h2>
          </div>
          <div className="p-6">
            <div className="whitespace-pre-wrap bg-muted/50 p-4 rounded-md">
              {archiveLink.markdown_content}
            </div>
          </div>
        </div>
      )}

      {archiveLink.clean_text && (
        <div className="border rounded-lg overflow-hidden mb-6">
          <div className="px-6 py-4 border-b">
            <h2 className="text-lg font-semibold">Clean Text</h2>
          </div>
          <div className="p-6">
            <div className="whitespace-pre-wrap bg-muted/50 p-4 rounded-md">
              {archiveLink.clean_text}
            </div>
          </div>
        </div>
      )}

      {archiveLink.context && (
        <div className="border rounded-lg overflow-hidden mb-6">
          <div className="px-6 py-4 border-b">
            <h2 className="text-lg font-semibold">Context</h2>
          </div>
          <div className="p-6">
            <div className="whitespace-pre-wrap bg-muted/50 p-4 rounded-md">
              {archiveLink.context}
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="border rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b">
            <h2 className="text-lg font-semibold">Metadata</h2>
          </div>
          <div className="p-6 space-y-2">
            <div>
              <p className="text-sm text-muted-foreground">Created At</p>
              <p>{new Date(archiveLink.created_at).toLocaleString()}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Updated At</p>
              <p>{new Date(archiveLink.updated_at).toLocaleString()}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Crawled At</p>
              <p>{archiveLink.crawled_at ? new Date(archiveLink.crawled_at).toLocaleString() : 'N/A'}</p>
            </div>
            {archiveLink.parent_id && (
              <div>
                <p className="text-sm text-muted-foreground">Parent ID</p>
                <p>
                  <Link 
                    to={`/archive-links/${archiveLink.parent_id}`}
                    className="text-blue-600 hover:underline"
                  >
                    {archiveLink.parent_id}
                  </Link>
                </p>
              </div>
            )}
          </div>
        </div>

        <div className="border rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b">
            <h2 className="text-lg font-semibold">Link Attributes</h2>
          </div>
          <div className="p-6 space-y-2">
            <div>
              <p className="text-sm text-muted-foreground">Rel</p>
              <p>{archiveLink.rel || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Target</p>
              <p>{archiveLink.target || 'N/A'}</p>
            </div>
          </div>
        </div>
      </div>

      {archiveLink.page_metadata && Object.keys(archiveLink.page_metadata).length > 0 && (
        <div className="border rounded-lg overflow-hidden mb-6">
          <div className="px-6 py-4 border-b">
            <h2 className="text-lg font-semibold">Page Metadata</h2>
          </div>
          <div className="p-6">
            <pre className="bg-muted/50 p-4 rounded-md overflow-auto max-h-96">
              {JSON.stringify(archiveLink.page_metadata, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
}

export default ArchiveLinkDetail;
