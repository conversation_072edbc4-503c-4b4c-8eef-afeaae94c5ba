import { apiRequest } from '@/config/httpClient';

export interface ArchiveLink {
  id: number;
  url: string;
  text: string | null;
  title: string | null;
  context: string | null;
  link_type: string;
  rel: string | null;
  target: string | null;
  is_image: boolean;
  parent_id: number | null;
  content: string | null;
  markdown_content: string | null;
  clean_text: string | null;
  status_code: number | null;
  content_type: string | null;
  file_size: number | null;
  domain: string;
  page_metadata: Record<string, any> | null;
  created_at: string;
  updated_at: string;
  crawled_at: string | null;
}

export interface ArchiveLinksResponse {
  archive_links: ArchiveLink[];
  total: number;
  skip: number;
  limit: number;
}

export interface ArchiveLinksParams {
  skip?: number;
  limit?: number;
  parent_id?: string;
  domain?: string;
  link_type?: string;
  is_image?: string;
  status_code?: string;
  search?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export const fetchArchiveLinks = async (params: ArchiveLinksParams = {}): Promise<ArchiveLinksResponse> => {
  return apiRequest<ArchiveLinksResponse>({
    method: 'get',
    url: '/archive-links',
    params: {
      skip: params.skip,
      limit: params.limit,
      parent_id: params.parent_id,
      domain: params.domain,
      link_type: params.link_type,
      is_image: params.is_image,
      status_code: params.status_code,
      search: params.search,
      sort_by: params.sort_by,
      sort_order: params.sort_order,
    },
  });
};

export const getArchiveLinkById = async (id: number): Promise<ArchiveLink> => {
  return apiRequest<ArchiveLink>({
    method: 'get',
    url: `/archive-links/${id}`,
  });
};
