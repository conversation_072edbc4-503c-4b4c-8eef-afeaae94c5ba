import { ErrorDisplay } from "@/components/error-display";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { useArchiveLinksQuery } from "./hooks/useArchiveLinksQuery";
import { ArchiveLinksTable } from "./components/ArchiveLinksTable";

export default function ArchiveLinksPage() {
  const { 
    data, 
    isLoading, 
    isError, 
    error, 
    isRefetching, 
    refetch 
  } = useArchiveLinksQuery();

  if (isError) {
    return <ErrorDisplay error={error} />;
  }

  const archiveLinks = data?.archive_links || [];
  const total = data?.total || 0;

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Archive Links</h1>
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => refetch()}
            disabled={isRefetching}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isRefetching ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>
      <ArchiveLinksTable 
        archiveLinks={archiveLinks} 
        loading={isLoading || isRefetching} 
        total={total}
      />
    </div>
  );
}
