import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig } from 'axios';

// Get API base URL from environment variables with a fallback to development URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';

// Create a custom Axios instance with default config
const createAxiosInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: API_BASE_URL,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  });

  // Add request interceptor for auth tokens, logging, etc.
  instance.interceptors.request.use(
    (config) => {
      // You can add auth tokens or other headers here if needed
      // const token = localStorage.getItem('authToken');
      // if (token) {
      //   config.headers.Authorization = `Bearer ${token}`;
      // }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Add response interceptor for error handling
  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      // Handle common errors here (e.g., 401 Unauthorized)
      // if (error.response?.status === 401) {
      //   // Handle unauthorized access
      // }
      return Promise.reject(error);
    }
  );

  return instance;
};

// Export the configured axios instance
export const httpClient = createAxiosInstance();

// Helper function to make API requests with proper typing
export const apiRequest = async <T>(
  config: AxiosRequestConfig
): Promise<T> => {
  try {
    const response = await httpClient.request<T>(config);
    return response.data;
  } catch (error) {
    // You can add more specific error handling here
    console.error('API Request Error:', error);
    throw error;
  }
};

// Export for direct use if needed
export default httpClient;
