import "./App.css";
import { createBrowserRouter, RouterProvider } from "react-router";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import MainLayout from "./layouts/MainLayout";
import HostsPage from "./routes/hosts/HostsPage";
import ParentLinksPage from "./routes/parent-links/ParentLinksPage";
import ArchiveLinksPage from "./routes/archive-links/ArchiveLinksPage";
import ArchiveLinkDetail from "./routes/archive-links/pages/ArchiveLinkDetail";
import { Toaster } from "@/components/ui/sonner"

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

function App() {
  const router = createBrowserRouter([
    {
      path: "/",
      Component: MainLayout,
      children: [
        {
          index: true,
          Component: () => <h1>Home</h1>,
        },
        {
          path: "hosts",
          Component: () => <HostsPage />,
        },
        {
          path: "parent-links",
          Component: () => <ParentLinksPage />,
        },
        {
          path: "archive-links",
          children: [
            {
              index: true,
              Component: () => <ArchiveLinksPage />,
            },
            {
              path: ":id",
              Component: () => <ArchiveLinkDetail />,
            },
          ],
        },
      ],
    },
  ]);

  return (
    <QueryClientProvider client={queryClient}>
      <RouterProvider router={router} />
      <ReactQueryDevtools initialIsOpen={false} />
      <Toaster />
    </QueryClientProvider>
  );
}

export default App;
