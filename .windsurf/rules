# Clawer System - Windsurf Rules Configuration

## Project Overview
This is a distributed web crawling system with multiple components:
- **Python FastAPI crawler service** with Celery task queue
- **NestJS admin API** for management operations  
- **React TypeScript admin UI** with shadcn/ui components
- **PostgreSQL database** with shared models
- **Docker containerized** microservices architecture

## Architecture & Structure

### Core Components
- `services/crawler/` - Python FastAPI crawler service with Celery workers
- `admin-api/` - NestJS TypeScript API for admin operations
- `admin-ui/` - React TypeScript dashboard with Vite and Tailwind CSS
- `admin-ui-crs/` - Secondary React admin interface
- `crawler_shared/` - Shared Python models and database configuration
- Dock<PERSON> Compose orchestrates all services with PostgreSQL and RabbitMQ

### Database Models
- **Host** - Domains/hosts that are crawled (one-to-many with ParentLink)
- **ParentLink** - Original URLs and scraped content (one-to-many with ArchiveLink)  
- **ArchiveLink** - Links extracted from crawled pages

## Technology Stack Rules

### Python/FastAPI (Crawler Service)
- Use FastAPI for REST API endpoints with automatic OpenAPI documentation
- Follow Pydantic schemas for request/response validation
- Use SQLAlchemy 2.0+ with async patterns for database operations
- Implement Celery for background task processing with RabbitMQ broker
- Use Alembic for database migrations
- Structure: `app/routers/`, `app/services/`, `app/schemas/`, `app/models/`
- Follow dependency injection patterns with FastAPI's `Depends()`
- Use type hints consistently throughout Python code

### TypeScript/NestJS (Admin API)
- Follow NestJS patterns with decorators and dependency injection
- Use TypeORM for database operations with PostgreSQL
- Implement proper DTOs for request/response validation
- Use Jest for testing with proper mocking
- Follow modular architecture with controllers, services, and entities
- Use environment configuration with `@nestjs/config`
- Implement proper error handling and HTTP status codes

### React/TypeScript (Admin UI)
- Use functional components with TypeScript interfaces
- Follow shadcn/ui component patterns and design system
- Use TanStack Query (react-query) for server state management
- Use Zustand for client state management
- Use React Hook Form with Zod validation for forms
- Use Tailwind CSS for styling with consistent design tokens
- Structure: `components/ui/`, `components/shared/`, `hooks/`, `services/`, `stores/`
- Prefer named exports for components
- Use Vite for build tooling and development server

## Development Patterns

### Code Organization
- Keep shared database models in `crawler_shared/` for consistency
- Use proper separation of concerns: routers → services → database
- Implement proper error handling at each layer
- Use dependency injection patterns consistently
- Follow RESTful API conventions for all endpoints

### Database Operations
- Use transactions for multi-table operations
- Implement proper foreign key relationships
- Use database migrations for schema changes
- Follow consistent naming conventions for tables and columns
- Implement proper indexing for performance

### Testing Strategy
- Write unit tests for services and business logic
- Use integration tests for API endpoints
- Mock external dependencies properly
- Test database operations with proper setup/teardown
- Use proper test data factories and fixtures

### Docker & Deployment
- Use multi-stage builds for production images
- Implement proper health checks for all services
- Use environment variables for configuration
- Follow security best practices for container images
- Implement proper logging and monitoring

## API Design Rules

### REST API Conventions
- Use consistent URL patterns: `/api/v1/{resource}/`
- Implement full CRUD operations where appropriate
- Use proper HTTP status codes and error responses
- Include pagination for list endpoints
- Implement proper request/response validation
- Use consistent error response format across all APIs

### Data Validation
- Use Pydantic schemas in FastAPI for Python validation
- Use DTOs and class-validator in NestJS for TypeScript validation
- Use Zod schemas in React for client-side validation
- Implement consistent validation error messages
- Validate all user inputs at API boundaries

## Performance & Scalability

### Async Operations
- Use async/await patterns consistently in Python and TypeScript
- Implement proper connection pooling for databases
- Use Celery for long-running background tasks
- Implement proper caching strategies where appropriate
- Use proper pagination for large datasets

### Monitoring & Logging
- Implement structured logging across all services
- Use proper log levels (DEBUG, INFO, WARN, ERROR)
- Include correlation IDs for request tracing
- Monitor Celery task queues and worker health
- Implement proper error tracking and alerting

## Security Considerations
- Validate all inputs at API boundaries
- Use proper authentication and authorization
- Implement rate limiting for public endpoints
- Use HTTPS in production environments
- Follow OWASP security guidelines
- Sanitize data before database operations

## Code Quality Standards
- Use ESLint and Prettier for TypeScript/JavaScript code formatting
- Use Black and isort for Python code formatting
- Implement pre-commit hooks for code quality checks
- Use TypeScript strict mode for better type safety
- Write comprehensive JSDoc/docstring documentation
- Follow consistent naming conventions across all languages
