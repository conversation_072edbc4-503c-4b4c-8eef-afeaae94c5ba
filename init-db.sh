#!/bin/bash
set -e

# Create additional databases
echo "Creating additional databases..."
for db in $(echo "${POSTGRES_MULTIPLE_DATABASES}" | tr ',' ' '); do
    echo "Creating database: $db"
    psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
        CREATE DATABASE "$db";
        GRANT ALL PRIVILEGES ON DATABASE "$db" TO "$POSTGRES_USER";
EOSQL
done

echo "Databases created successfully!"
