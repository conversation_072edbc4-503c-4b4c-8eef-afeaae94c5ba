from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, desc, asc, func
from typing import List, Optional, Dict, Any
from datetime import datetime
from urllib.parse import urlparse

from crawler_shared.models import ArchiveLink, ParentLink
from app.schemas.archive_link import (
    ArchiveLinkCreate, ArchiveLinkUpdate, ArchiveLinkResponse,
    ArchiveLinkListResponse, ArchiveLinkStatsResponse,
    ArchiveLinkWithParent
)


class ArchiveLinkService:
    """Service class for ArchiveLink CRUD operations."""

    @staticmethod
    def create_archive_link(db: Session, archive_link_data: ArchiveLinkCreate) -> ArchiveLink:
        """
        Create a new archive link.
        
        Args:
            db: Database session
            archive_link_data: Archive link data to create
            
        Returns:
            ArchiveLink: The created archive link
            
        Raises:
            ValueError: If validation fails
        """
        # Extract domain from URL if not provided
        if not archive_link_data.domain and archive_link_data.url:
            try:
                parsed_url = urlparse(archive_link_data.url)
                archive_link_data.domain = parsed_url.netloc
            except Exception:
                pass

        # Validate parent_id exists if provided
        if archive_link_data.parent_id:
            parent = db.query(ParentLink).filter(ParentLink.id == archive_link_data.parent_id).first()
            if not parent:
                raise ValueError(f"Parent link with ID {archive_link_data.parent_id} not found")

        # Create the archive link
        db_archive_link = ArchiveLink(**archive_link_data.dict())
        db.add(db_archive_link)
        db.commit()
        db.refresh(db_archive_link)
        
        return db_archive_link

    @staticmethod
    def get_archive_link_by_id(db: Session, archive_link_id: int) -> Optional[ArchiveLink]:
        """
        Get an archive link by ID.
        
        Args:
            db: Database session
            archive_link_id: ID of the archive link to retrieve
            
        Returns:
            ArchiveLink or None: The archive link if found
        """
        return db.query(ArchiveLink).filter(ArchiveLink.id == archive_link_id).first()

    @staticmethod
    def get_archive_link_with_parent(db: Session, archive_link_id: int) -> Optional[ArchiveLink]:
        """
        Get an archive link with its parent information.
        
        Args:
            db: Database session
            archive_link_id: ID of the archive link to retrieve
            
        Returns:
            ArchiveLink or None: The archive link with parent if found
        """
        return db.query(ArchiveLink)\
                 .options(joinedload(ArchiveLink.parent))\
                 .filter(ArchiveLink.id == archive_link_id)\
                 .first()

    @staticmethod
    def get_archive_links(
        db: Session,
        skip: int = 0,
        limit: int = 100,
        parent_id: Optional[int] = None,
        domain: Optional[str] = None,
        link_type: Optional[str] = None,
        is_image: Optional[int] = None,
        status_code: Optional[int] = None,
        search: Optional[str] = None,
        sort_by: str = "crawled_at",
        sort_order: str = "desc"
    ) -> ArchiveLinkListResponse:
        """
        Get paginated list of archive links with optional filtering and sorting.
        
        Args:
            db: Database session
            skip: Number of records to skip for pagination
            limit: Number of records to return
            parent_id: Filter by parent link ID
            domain: Filter by domain
            link_type: Filter by link type
            is_image: Filter by image links (0 or 1)
            status_code: Filter by HTTP status code
            search: Search term for URL, text, title, or context
            sort_by: Field to sort by
            sort_order: Sort order (asc or desc)
            
        Returns:
            ArchiveLinkListResponse: Paginated list of archive links
        """
        query = db.query(ArchiveLink)

        # Apply filters
        if parent_id is not None:
            query = query.filter(ArchiveLink.parent_id == parent_id)
        
        if domain:
            query = query.filter(ArchiveLink.domain.ilike(f"%{domain}%"))
        
        if link_type:
            query = query.filter(ArchiveLink.link_type == link_type)
        
        if is_image is not None:
            query = query.filter(ArchiveLink.is_image == is_image)
        
        if status_code is not None:
            query = query.filter(ArchiveLink.status_code == status_code)
        
        if search:
            search_filter = or_(
                ArchiveLink.url.ilike(f"%{search}%"),
                ArchiveLink.text.ilike(f"%{search}%"),
                ArchiveLink.title.ilike(f"%{search}%"),
                ArchiveLink.context.ilike(f"%{search}%"),
                ArchiveLink.clean_text.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)

        # Get total count before applying pagination
        total = query.count()

        # Apply sorting
        sort_column = getattr(ArchiveLink, sort_by, ArchiveLink.crawled_at)
        if sort_order.lower() == "desc":
            query = query.order_by(desc(sort_column))
        else:
            query = query.order_by(asc(sort_column))

        # Select only the fields we need for the response
        selected_fields = [
            ArchiveLink.id,
            ArchiveLink.url,
            ArchiveLink.text,
            ArchiveLink.title,
            ArchiveLink.context,
            ArchiveLink.link_type,
            ArchiveLink.rel,
            ArchiveLink.target,
            ArchiveLink.is_image,
            ArchiveLink.crawled_at,
            ArchiveLink.status_code,
            ArchiveLink.domain,
            ArchiveLink.content_type,
            ArchiveLink.file_size,
            ArchiveLink.parent_id
        ]
        
        # Apply pagination with selected fields
        archive_links = query.with_entities(*selected_fields).offset(skip).limit(limit).all()
        
        # Convert to list of dictionaries for Pydantic model
        archive_links_dicts = [
            {
                'id': link.id,
                'url': link.url,
                'text': link.text,
                'title': link.title,
                'context': link.context,
                'link_type': link.link_type,
                'rel': link.rel,
                'target': link.target,
                'is_image': link.is_image,
                'crawled_at': link.crawled_at,
                'status_code': link.status_code,
                'domain': link.domain,
                'content_type': link.content_type,
                'file_size': link.file_size,
                'parent_id': link.parent_id
            }
            for link in archive_links
        ]

        return ArchiveLinkListResponse(
            archive_links=archive_links_dicts,
            total=total,
            skip=skip,
            limit=limit,
            has_next=skip + limit < total,
            has_prev=skip > 0
        )

    @staticmethod
    def update_archive_link(
        db: Session, 
        archive_link_id: int, 
        archive_link_data: ArchiveLinkUpdate
    ) -> Optional[ArchiveLink]:
        """
        Update an archive link.
        
        Args:
            db: Database session
            archive_link_id: ID of the archive link to update
            archive_link_data: Updated archive link data
            
        Returns:
            ArchiveLink or None: The updated archive link if found
            
        Raises:
            ValueError: If validation fails
        """
        db_archive_link = db.query(ArchiveLink).filter(ArchiveLink.id == archive_link_id).first()
        if not db_archive_link:
            return None

        # Validate parent_id exists if provided
        if archive_link_data.parent_id is not None:
            parent = db.query(ParentLink).filter(ParentLink.id == archive_link_data.parent_id).first()
            if not parent:
                raise ValueError(f"Parent link with ID {archive_link_data.parent_id} not found")

        # Update fields
        update_data = archive_link_data.dict(exclude_unset=True)
        
        # Extract domain from URL if URL is being updated and domain is not provided
        if 'url' in update_data and 'domain' not in update_data:
            try:
                parsed_url = urlparse(update_data['url'])
                update_data['domain'] = parsed_url.netloc
            except Exception:
                pass

        for field, value in update_data.items():
            setattr(db_archive_link, field, value)

        db.commit()
        db.refresh(db_archive_link)
        
        return db_archive_link

    @staticmethod
    def delete_archive_link(db: Session, archive_link_id: int) -> bool:
        """
        Delete an archive link.
        
        Args:
            db: Database session
            archive_link_id: ID of the archive link to delete
            
        Returns:
            bool: True if deleted, False if not found
        """
        db_archive_link = db.query(ArchiveLink).filter(ArchiveLink.id == archive_link_id).first()
        if not db_archive_link:
            return False

        db.delete(db_archive_link)
        db.commit()
        
        return True

    @staticmethod
    def get_archive_link_stats(db: Session) -> ArchiveLinkStatsResponse:
        """
        Get statistics about archive links.
        
        Args:
            db: Database session
            
        Returns:
            ArchiveLinkStatsResponse: Statistics about archive links
        """
        total_archive_links = db.query(ArchiveLink).count()
        total_with_content = db.query(ArchiveLink).filter(ArchiveLink.content.isnot(None)).count()
        total_images = db.query(ArchiveLink).filter(ArchiveLink.is_image == 1).count()
        total_external_links = db.query(ArchiveLink).filter(ArchiveLink.link_type == 'external').count()
        total_internal_links = db.query(ArchiveLink).filter(ArchiveLink.link_type == 'internal').count()
        
        # Calculate average file size
        avg_file_size_result = db.query(func.avg(ArchiveLink.file_size))\
                                 .filter(ArchiveLink.file_size.isnot(None))\
                                 .scalar()
        avg_file_size = float(avg_file_size_result) if avg_file_size_result else None
        
        # Count unique domains
        domains_count = db.query(func.count(func.distinct(ArchiveLink.domain)))\
                          .filter(ArchiveLink.domain.isnot(None))\
                          .scalar()
        
        # Link type distribution
        link_types = db.query(ArchiveLink.link_type, func.count(ArchiveLink.link_type))\
                       .filter(ArchiveLink.link_type.isnot(None))\
                       .group_by(ArchiveLink.link_type)\
                       .all()
        
        link_type_distribution = {link_type: count for link_type, count in link_types}
        
        # Status code distribution
        status_codes = db.query(ArchiveLink.status_code, func.count(ArchiveLink.status_code))\
                         .filter(ArchiveLink.status_code.isnot(None))\
                         .group_by(ArchiveLink.status_code)\
                         .all()
        
        status_code_distribution = {str(code): count for code, count in status_codes}
        
        return ArchiveLinkStatsResponse(
            total_archive_links=total_archive_links,
            total_with_content=total_with_content,
            total_images=total_images,
            total_external_links=total_external_links,
            total_internal_links=total_internal_links,
            avg_file_size=avg_file_size,
            domains_count=domains_count,
            link_type_distribution=link_type_distribution,
            status_code_distribution=status_code_distribution
        )

    @staticmethod
    def get_archive_links_by_parent(
        db: Session,
        parent_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> ArchiveLinkListResponse:
        """
        Get archive links for a specific parent link.
        
        Args:
            db: Database session
            parent_id: ID of the parent link
            skip: Number of records to skip for pagination
            limit: Number of records to return
            
        Returns:
            ArchiveLinkListResponse: Paginated list of archive links for the parent
        """
        return ArchiveLinkService.get_archive_links(
            db=db,
            skip=skip,
            limit=limit,
            parent_id=parent_id
        )
