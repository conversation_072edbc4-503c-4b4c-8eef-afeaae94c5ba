from pydantic import BaseModel, <PERSON>, validator
from typing import List, Optional, Dict, Any
from datetime import datetime
from urllib.parse import urlparse


class ArchiveLinkBase(BaseModel):
    """Base schema for ArchiveLink with common fields."""
    url: str = Field(..., description="The URL of the extracted link")
    text: Optional[str] = Field(None, description="Link text content")
    title: Optional[str] = Field(None, description="Link title attribute")
    context: Optional[str] = Field(None, description="Surrounding text context")
    link_type: Optional[str] = Field(None, description="Link type (internal/external)")
    rel: Optional[str] = Field(None, description="rel attribute (nofollow, noopener, etc.)")
    target: Optional[str] = Field(None, description="target attribute (_blank, etc.)")
    is_image: int = Field(0, description="Whether this is an image link (0 = no, 1 = yes)")
    parent_id: Optional[int] = Field(None, description="Foreign key to parent_link table")
    
    # For crawl_and_archive_task: store scraped content directly
    content: Optional[str] = Field(None, description="Raw HTML content")
    markdown_content: Optional[str] = Field(None, description="Clean markdown content")
    clean_text: Optional[str] = Field(None, description="Text content without HTML tags")
    status_code: Optional[int] = Field(None, description="HTTP status code")
    content_type: Optional[str] = Field(None, description="Content-Type header")
    file_size: Optional[int] = Field(None, description="Size in bytes")
    domain: Optional[str] = Field(None, description="Extracted domain for easier querying")
    page_metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata as JSON")

   

    @validator('is_image')
    def validate_is_image(cls, v):
        """Validate is_image field."""
        if v not in [0, 1]:
            raise ValueError('is_image must be 0 or 1')
        return v

    @validator('link_type')
    def validate_link_type(cls, v):
        """Validate link_type field."""
        if v is not None:
            valid_types = ['internal', 'external', 'anchor', 'mailto', 'tel', 'file']
            if v not in valid_types:
                raise ValueError(f'link_type must be one of: {", ".join(valid_types)}')
        return v

    @validator('status_code')
    def validate_status_code(cls, v):
        """Validate HTTP status code."""
        if v is not None:
            if not (100 <= v <= 599):
                raise ValueError('status_code must be between 100 and 599')
        return v

    @validator('file_size')
    def validate_file_size(cls, v):
        """Validate file size."""
        if v is not None:
            if v < 0:
                raise ValueError('file_size must be non-negative')
        return v

    @validator('parent_id')
    def validate_parent_id(cls, v):
        """Validate parent_id."""
        if v is not None:
            if v <= 0:
                raise ValueError('parent_id must be positive')
        return v


class ArchiveLinkCreate(ArchiveLinkBase):
    """Schema for creating a new archive link."""
    pass


class ArchiveLinkUpdate(BaseModel):
    """Schema for updating an archive link."""
    url: Optional[str] = Field(None, description="The URL of the extracted link")
    text: Optional[str] = Field(None, description="Link text content")
    title: Optional[str] = Field(None, description="Link title attribute")
    context: Optional[str] = Field(None, description="Surrounding text context")
    link_type: Optional[str] = Field(None, description="Link type (internal/external)")
    rel: Optional[str] = Field(None, description="rel attribute (nofollow, noopener, etc.)")
    target: Optional[str] = Field(None, description="target attribute (_blank, etc.)")
    is_image: Optional[int] = Field(None, description="Whether this is an image link (0 = no, 1 = yes)")
    parent_id: Optional[int] = Field(None, description="Foreign key to parent_link table")
    
    # For crawl_and_archive_task: store scraped content directly
    content: Optional[str] = Field(None, description="Raw HTML content")
    markdown_content: Optional[str] = Field(None, description="Clean markdown content")
    clean_text: Optional[str] = Field(None, description="Text content without HTML tags")
    status_code: Optional[int] = Field(None, description="HTTP status code")
    content_type: Optional[str] = Field(None, description="Content-Type header")
    file_size: Optional[int] = Field(None, description="Size in bytes")
    domain: Optional[str] = Field(None, description="Extracted domain for easier querying")
    page_metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata as JSON")

    @validator('url')
    def validate_url(cls, v):
        """Validate that url is a valid URL if provided."""
        if v is not None:
            try:
                parsed = urlparse(v)
                if not parsed.scheme or not parsed.netloc:
                    raise ValueError('url must be a valid URL with scheme and netloc')
            except Exception:
                raise ValueError('url must be a valid URL')
        return v

    @validator('is_image')
    def validate_is_image(cls, v):
        """Validate is_image field."""
        if v is not None and v not in [0, 1]:
            raise ValueError('is_image must be 0 or 1')
        return v

    @validator('link_type')
    def validate_link_type(cls, v):
        """Validate link_type field."""
        if v is not None:
            valid_types = ['internal', 'external', 'anchor', 'mailto', 'tel', 'file']
            if v not in valid_types:
                raise ValueError(f'link_type must be one of: {", ".join(valid_types)}')
        return v

    @validator('status_code')
    def validate_status_code(cls, v):
        """Validate HTTP status code."""
        if v is not None:
            if not (100 <= v <= 599):
                raise ValueError('status_code must be between 100 and 599')
        return v

    @validator('file_size')
    def validate_file_size(cls, v):
        """Validate file size."""
        if v is not None:
            if v < 0:
                raise ValueError('file_size must be non-negative')
        return v

    @validator('parent_id')
    def validate_parent_id(cls, v):
        """Validate parent_id."""
        if v is not None:
            if v <= 0:
                raise ValueError('parent_id must be positive')
        return v


class ArchiveLinkResponse(ArchiveLinkBase):
    """Schema for archive link response."""
    id: int
    crawled_at: datetime

    class Config:
        from_attributes = True


class ArchiveLinkWithParent(ArchiveLinkResponse):
    """Schema for archive link response including parent information."""
    parent: Optional['ParentLinkResponse'] = None

    class Config:
        from_attributes = True


class ArchiveLinkListResponse(BaseModel):
    """Schema for paginated archive link list response."""
    archive_links: List[ArchiveLinkResponse]
    total: int
    skip: int
    limit: int
    has_next: bool
    has_prev: bool


class ArchiveLinkStatsResponse(BaseModel):
    """Schema for archive link statistics response."""
    total_archive_links: int
    total_with_content: int
    total_images: int
    total_external_links: int
    total_internal_links: int
    avg_file_size: Optional[float] = None
    domains_count: int
    link_type_distribution: Dict[str, int]
    status_code_distribution: Dict[str, int]


# Simple schema to avoid circular imports
class ParentLinkResponse(BaseModel):
    """Simple schema for parent link response in archive link context."""
    id: int
    original_url: str
    final_url: Optional[str] = None
    title: Optional[str] = None
    status_code: Optional[int] = None
    crawled_at: datetime
    domain: Optional[str] = None

    class Config:
        from_attributes = True


# Update forward references
ArchiveLinkWithParent.model_rebuild()
